import { useCallback } from 'react';
import { useRealtimeQuery } from '@/hooks/useRealtimeQuery';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useClerkSupabaseSync } from '@/hooks/useClerkSupabaseSync';
import { getDomainFromWindow } from '@/lib/domain';
import { useAuth as useClerkAuth } from '@clerk/nextjs';
import type { Database } from '@/types/supabase';

export type TenantDepartment =
  Database['public']['Tables']['tenant_departments']['Row'];

/**
 * Uses the existing useRealtimeQuery pattern for consistency
 */
export function useDepartments(tenantId: string | null, enabled = true) {
  const { isAdmin, isSuperAdmin } = useAuth();
  const { getToken } = useClerkAuth();

  // Get sync status to prevent race conditions
  const syncTenantId =
    typeof window !== 'undefined' ? getDomainFromWindow(window).tenantId : null;
  const syncStatus = useClerkSupabaseSync(syncTenantId);

  // Only admin/super_admin can access department management
  const canManageDepartments = isAdmin || isSuperAdmin;

  return useRealtimeQuery<TenantDepartment>(
    ['departments', tenantId || ''],
    async () => {
      if (!tenantId) {
        throw new Error('Tenant ID is required');
      }

      console.log('🔍 Fetching departments for tenant:', tenantId);

      // CRITICAL FIX: Add authentication headers to prevent intermittent auth errors
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      try {
        const token =
          (await getToken({ template: 'supabase' })) || (await getToken());
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }
      } catch (error) {
        console.warn('⚠️ Failed to get authentication token:', error);
      }

      // Call the API endpoint instead of querying the database directly
      const response = await fetch('/api/departments', { headers });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Error fetching departments:', errorData);
        throw new Error(errorData.error || 'Failed to fetch departments');
      }

      const data = await response.json();
      console.log('✅ Fetched departments:', data?.length || 0);
      return (data || []) as TenantDepartment[];
    },
    'tenant_departments',
    {
      filter: tenantId ? `tenant_id=eq.${tenantId}` : '',
      queryOptions: {
        enabled:
          enabled &&
          !!tenantId &&
          canManageDepartments &&
          syncStatus.isComplete &&
          !syncStatus.isLoading,
        staleTime: 1000 * 60 * 5, // 5 minutes - departments don't change frequently
        gcTime: 1000 * 60 * 30, // 30 minutes
      },
    }
  );
}

/**
 * Hook for fetching active departments only (for dropdowns, etc.)
 * This can be used by non-admin users for ticket creation
 *
 * Real-time updates: When a department's is_active status changes,
 * the real-time subscription will automatically update the cache
 * and this hook will re-filter to show only active departments.
 */
export function useActiveDepartments(tenantId: string | null, enabled = true) {
  const { getToken } = useClerkAuth();

  // Get sync status to prevent race conditions
  const syncTenantId =
    typeof window !== 'undefined' ? getDomainFromWindow(window).tenantId : null;
  const syncStatus = useClerkSupabaseSync(syncTenantId);

  const query = useRealtimeQuery<TenantDepartment>(
    ['departments', 'active', tenantId || ''],
    async () => {
      if (!tenantId) {
        throw new Error('Tenant ID is required');
      }

      console.log('🔍 Fetching active departments for tenant:', tenantId);

      // CRITICAL FIX: Add authentication headers to prevent intermittent auth errors
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      try {
        const token =
          (await getToken({ template: 'supabase' })) || (await getToken());
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }
      } catch (error) {
        console.warn('⚠️ Failed to get authentication token:', error);
      }

      // Call the API endpoint and filter for active departments
      const response = await fetch('/api/departments', { headers });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Error fetching active departments:', errorData);
        throw new Error(errorData.error || 'Failed to fetch departments');
      }

      const allDepartments = await response.json();
      const activeDepartments = allDepartments.filter(
        (dept: TenantDepartment) => dept.is_active
      );

      console.log(
        '✅ Fetched active departments:',
        activeDepartments?.length || 0,
        'out of',
        allDepartments?.length || 0,
        'total departments'
      );
      return (activeDepartments || []) as TenantDepartment[];
    },
    'tenant_departments',
    {
      filter: tenantId ? `tenant_id=eq.${tenantId}` : '',
      queryOptions: {
        enabled:
          enabled &&
          !!tenantId &&
          syncStatus.isComplete &&
          !syncStatus.isLoading,
        staleTime: 1000 * 60 * 5, // 5 minutes - reduced for faster real-time updates
        gcTime: 1000 * 60 * 30, // 30 minutes
      },
    }
  );

  // Add smart background validation method with throttling
  const validateInBackground = useCallback(async () => {
    if (!tenantId || query.isFetching) return;

    // Smart caching: Only refetch if data is stale (older than 30 seconds)
    const dataAge = Date.now() - (query.dataUpdatedAt || 0);
    const isStale = dataAge > 30 * 1000; // 30 seconds

    if (!isStale) {
      console.log('🔄 Smart validation: Data is fresh, skipping refetch');
      return;
    }

    console.log('🔄 Smart background validation triggered for departments');
    await query.refetch();
  }, [tenantId, query]);

  return {
    ...query,
    validateInBackground,
  };
}
