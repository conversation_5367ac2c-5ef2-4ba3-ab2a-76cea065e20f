'use client';

import { useUser, useClerk } from '@clerk/nextjs';
import { getDomainFromWindow } from '@/lib/domain';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo, useRef } from 'react';
import { useTicketingUIActions } from '@/features/ticketing/store/use-ticketing-store';
import { useQueryClient } from '@tanstack/react-query';
import { clearAllCaches } from '@/providers/ReactQueryProvider';

export interface AuthState {
  user: ReturnType<typeof useUser>['user'];
  isLoaded: boolean;
  isSignedIn: boolean;
  isTokenReady: boolean;
  tenantId: string | null;
  role: string;
  isSuperAdmin: boolean;
  isAdmin: boolean;
  isAgent: boolean;
  isUser: boolean;
  isLoggingOut: boolean;
}

export interface AuthActions {
  signOut: () => Promise<void>;
  navigate: (path: '/tickets' | '/sign-in' | '/sign-up') => void;
}

/**
 * Optimized authentication hook with modern 2025 patterns
 */
export function useAuth(): AuthState & AuthActions;
export function useAuth<T>(selector: (state: AuthState & AuthActions) => T): T;
export function useAuth<T>(
  selector?: (state: AuthState & AuthActions) => T
): T | (AuthState & AuthActions) {
  const { user, isLoaded, isSignedIn } = useUser();
  const { signOut: clerkSignOut } = useClerk();

  // CRITICAL FIX: Disable organization hook to prevent automatic API calls
  // The organization membership is handled through our sync process instead
  // This prevents Clerk from making API calls that cause authentication errors

  // Get tenant ID directly from domain to avoid Zustand store issues
  const tenantId = useMemo(() => {
    if (typeof window === 'undefined') return null;
    const domainInfo = getDomainFromWindow(window);
    return domainInfo.tenantId;
  }, []);

  const router = useRouter();
  const queryClient = useQueryClient();

  // AbortController for canceling requests during logout
  const logoutAbortControllerRef = useRef<AbortController | null>(null);

  // Get store actions for clearing state on logout
  const resetUIState = useTicketingUIActions.useResetUIState();

  const role = useMemo(() => {
    if (!isSignedIn || !user) {
      return 'user';
    }

    // CRITICAL FIX: Check organization membership first, then fallback to public metadata
    // This ensures proper role detection for users with organization roles
    let clerkRole = 'user';

    // First, check organization membership for role
    if (
      user.organizationMemberships &&
      user.organizationMemberships.length > 0
    ) {
      // Get the role from the first organization membership
      const orgRole = user.organizationMemberships[0]?.role;
      if (orgRole) {
        clerkRole = orgRole;
      }
    }

    // Fallback to public metadata if no organization role found
    if (clerkRole === 'user' && user.publicMetadata?.role) {
      clerkRole = user.publicMetadata.role as string;
    }

    // Map Clerk roles to internal roles
    const roleMapping: Record<string, string> = {
      'org:super_admin': 'super_admin',
      'org:admin': 'admin',
      'org:agent': 'agent',
      'org:member': 'user',
      super_admin: 'super_admin',
      admin: 'admin',
      agent: 'agent',
      user: 'user',
    };

    const mappedRole = roleMapping[clerkRole.toLowerCase()] || 'user';

    // Fallback for specific super admin email
    if (
      mappedRole === 'user' &&
      user?.emailAddresses?.[0]?.emailAddress === '<EMAIL>'
    ) {
      return 'super_admin';
    }

    return mappedRole;
  }, [isSignedIn, user]);

  const isSuperAdmin = role === 'super_admin';
  const isAdmin = role === 'admin' || isSuperAdmin;
  const isAgent = role === 'agent';
  const isUser = role === 'user';

  // SIMPLIFIED FIX: Use isLoaded && isSignedIn as token ready indicator
  // This avoids the useEffect issue and provides a simpler solution
  const isTokenReady = isLoaded && isSignedIn;

  const signOut = useCallback(async () => {
    try {
      // Modern React 19 pattern: Optimized logout sequence
      logoutAbortControllerRef.current = new AbortController();

      // Clear local state first to prevent new API calls
      resetUIState();

      // Clear React Query cache - modern 2025 pattern
      queryClient.clear();

      // Clear all persistent caches (React Query + Dexie)
      await clearAllCaches();

      await clerkSignOut();

      // Instant navigation without loading states
      router.push('/sign-in');
    } catch (error) {
      console.error('Error during logout:', error);
      window.location.href = '/sign-in';
    } finally {
      logoutAbortControllerRef.current = null;
    }
  }, [clerkSignOut, router, resetUIState, queryClient]);

  const navigate = useCallback(
    (path: '/tickets' | '/sign-in' | '/sign-up') => {
      router.push(path);
    },
    [router]
  );

  // Check if logout is in progress
  const isLoggingOut = logoutAbortControllerRef.current !== null;

  const state = {
    user,
    isLoaded,
    isSignedIn: isSignedIn || false,
    isTokenReady,
    tenantId,
    role,
    isSuperAdmin,
    isAdmin,
    isAgent,
    isUser,
    isLoggingOut,
    signOut,
    navigate,
  };

  return selector ? selector(state) : state;
}

/**
 * Hook for checking if user has specific permissions
 */
export function usePermissions() {
  const { isSuperAdmin, isAdmin, isAgent, isUser } = useAuth();

  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (isSuperAdmin || isAdmin) return true;

      // Tenant features now handled by React Query - simplified for now

      switch (permission) {
        case 'tickets.view':
          return isUser || isAgent;
        case 'tickets.create':
          return isSuperAdmin || isAdmin || isUser;
        case 'tickets.update':
          return isAgent || isAdmin || isSuperAdmin;
        case 'tickets.delete':
          return isAdmin || isSuperAdmin;
        case 'tickets.assign':
          return isAdmin || isSuperAdmin;
        case 'tickets.priority.change':
          return isAdmin || isSuperAdmin;
        case 'tickets.department.change':
          return isAdmin || isSuperAdmin;
        case 'analytics.view':
          return isAgent || isAdmin || isSuperAdmin; // Simplified - tenant features via React Query
        case 'integrations.manage':
          return isAdmin || isSuperAdmin; // Simplified - tenant features via React Query
        case 'users.manage':
          return isAdmin || isSuperAdmin;
        case 'settings.manage':
          return isAdmin || isSuperAdmin;
        default:
          return false;
      }
    },
    [isSuperAdmin, isAdmin, isAgent, isUser]
  );

  const canAccessFeature = (): boolean => {
    // Simplified - tenant features now handled by React Query
    return isAdmin || isSuperAdmin;
  };

  return {
    hasPermission,
    canAccessFeature,
    isSuperAdmin,
    isAdmin,
    isAgent,
    isUser,
  };
}
