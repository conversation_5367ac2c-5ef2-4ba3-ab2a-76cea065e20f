import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const subdomain = searchParams.get('subdomain');
    const uuid = searchParams.get('uuid');

    if (!subdomain && !uuid) {
      return NextResponse.json(
        { error: 'Either subdomain or uuid parameter is required' },
        { status: 400 }
      );
    }

    // Query the tenants table by subdomain or UUID
    let query = supabase.from('tenants').select('*');

    if (subdomain) {
      query = query.eq('subdomain', subdomain);
    } else if (uuid) {
      query = query.eq('id', uuid);
    }

    const { data: tenant, error } = await query.single();

    if (error) {
      console.error('Error fetching tenant:', error);
      return NextResponse.json({ error: 'Tenant not found' }, { status: 404 });
    }

    if (!tenant) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 404 });
    }

    // Return the tenant UUID and data
    return NextResponse.json({
      tenantId: tenant.id,
      tenant: {
        id: tenant.id,
        name: tenant.name,
        subdomain: tenant.subdomain,
        domain: tenant.domain,
        createdAt: new Date(tenant.created_at),
        updatedAt: new Date(tenant.updated_at),
        settings: tenant.settings || {
          allowSignup: true,
          requireEmailVerification: true,
          maxUsers: 1000,
          features: ['tickets', 'analytics', 'integrations'],
        },
      },
    });
  } catch (error) {
    console.error('Unexpected error in tenant resolution:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
